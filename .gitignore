# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

.env

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work
go.work.sum

# the result of the go build
backend/**/output*
output/*

# Files generated by IDEs
.idea/
*.iml

# Vim swap files
*.swp

# Vscode files
.vscode

/patches
/oldimpl

.DS_Store

bin/*

docker/data/*

backend/static

node_modules
common/temp
.rush
.eslintcache


backend/conf/model/*.yaml

*.tsbuildinfo
