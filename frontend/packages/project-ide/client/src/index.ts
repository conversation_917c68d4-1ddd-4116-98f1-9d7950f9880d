/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export * from '@coze-project-ide/core';

export {
  type CustomTitleType,
  type ViewOptionRegisterService,
  type CustomPreferenceConfig,
  type CustomTitleChanged,
  LayoutPanelType,
  ToolbarAlign,
  ReactWidget,
  ViewManager,
  WidgetFactory,
  WidgetManager,
  CurrentResourceContext,
  ReactWidgetContext,
  ViewContribution,
  useCurrentWidget,
  useCurrentWidgetFromArea,
  useCurrentResource,
  Widget,
  StatefulWidget,
  ApplicationShell,
  LayoutRestorer,
  CustomPreferenceContribution,
  ViewService,
  FlowDockPanel,
  HoverService,
  MenuService,
  DebugService,
  DEBUG_BAR_DRAGGABLE,
  SplitWidget,
  BoxLayout,
  DockLayout,
  BoxPanel,
  SplitLayout,
  SplitPanel,
  createBoxLayout,
  createSplitLayout,
  PerfectScrollbar,
  DISABLE_HANDLE_EVENT,
  TabBarToolbar,
  ACTIVITY_BAR_CONTENT,
  ViewRenderer,
  DragService,
  CustomTabBar,
  TabBar,
  type DragPropsType,
  type PresetConfigType,
  type ToolbarItem,
} from '@coze-project-ide/view';
export { createDefaultPreset } from './create-default-preset';
export { type IDEClientOptions, IDEClientContext } from './types';
export * from './components';
