/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import type {
  MessageOperateType,
  MessageBizType,
} from '@coze-arch/bot-api/workflow_api';

export interface WsMessageProps {
  resId: string;
  extra: any;
  /**
   * 其他窗口执行保存传入的版本号
   */
  saveVersion?: string;
  operateType: MessageOperateType;
  bizType: MessageBizType;
}
