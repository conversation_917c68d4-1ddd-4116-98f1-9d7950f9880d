/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export const CUSTOM_TAB_BAR_CONTAINER = 'custom-tabBar-container';

// 前边的 action
export const PRE_TAB_BAR_ACTION_CONTAINER = 'pre-flow-tabBar-action-container';
// 后边的 action
export const TAB_BAR_ACTION_CONTAINER = 'flow-tabBar-action-container';
// tab bar 滚动区域
export const TAB_BAR_SCROLL_CONTAINER = 'flow-tabBar-scroll-container';

// toolbar
export const TAB_BAR_TOOLBAR = 'flow-toolbar-container';
export const TAB_BAR_TOOLBAR_ITEM = 'flow-toolbar-item';
export const DISABLE_HANDLE_EVENT = 'disable-handle-event';

/** DebugBar 可拖拽区域 classname */
export const DEBUG_BAR_DRAGGABLE = 'flow-debug-bar-draggable';
