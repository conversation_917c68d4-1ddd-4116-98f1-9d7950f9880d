/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import {
  type Resource,
  useIDEService,
  ResourceService,
} from '@coze-project-ide/core';

import { useCurrentWidget } from './use-current-widget';

export const CurrentResourceContext = React.createContext<Resource | undefined>(
  undefined,
);

export function useCurrentResource<T extends Resource>(): T {
  const currentResource = React.useContext(CurrentResourceContext);
  if (currentResource) {
    return currentResource as T;
  }
  const resourceService = useIDEService<ResourceService>(ResourceService);
  const widget = useCurrentWidget();
  const uri = widget.getResourceURI();
  if (!uri) {
    throw new Error('Cannot get uri from widget');
  }
  return resourceService.get(uri);
}
