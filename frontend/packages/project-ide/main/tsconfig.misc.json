{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "include": ["__tests__", "vitest.config.ts"], "exclude": ["**/node_modules", "./dist"], "references": [{"path": "./tsconfig.build.json"}], "compilerOptions": {"baseUrl": "./", "paths": {}, "jsx": "react", "isolatedModules": true, "strictNullChecks": true, "strictPropertyInitialization": false, "rootDir": "./", "outDir": "./dist"}}