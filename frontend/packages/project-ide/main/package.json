{"name": "@coze-project-ide/main", "version": "0.0.1", "author": "<EMAIL>", "main": "./src/index.tsx", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache --quiet"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/fetch-stream": "workspace:*", "@coze-arch/foundation-sdk": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-arch/idl": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-common/auth": "workspace:*", "@coze-common/auth-adapter": "workspace:*", "@coze-common/chat-uikit": "workspace:*", "@coze-common/resource-tree": "workspace:*", "@coze-project-ide/biz-components": "workspace:*", "@coze-project-ide/biz-data": "workspace:*", "@coze-project-ide/biz-plugin": "workspace:*", "@coze-project-ide/biz-plugin-registry-adapter": "workspace:*", "@coze-project-ide/biz-workflow": "workspace:*", "@coze-project-ide/framework": "workspace:*", "@coze-project-ide/ui-adapter": "workspace:*", "@coze-studio/components": "workspace:*", "@coze-studio/project-entity-adapter": "workspace:*", "@coze-studio/project-publish": "workspace:*", "@coze-studio/publish-manage-hooks": "workspace:*", "@coze-studio/user-store": "workspace:*", "@coze-workflow/base": "workspace:*", "@coze/api": "1.1.0-beta.4", "ahooks": "^3.7.8", "classnames": "^2.3.2", "dayjs": "^1.11.7", "dexie": "^4.0.8", "inversify": "^6.0.1", "lodash-es": "^4.17.21", "react-error-boundary": "^4.0.9", "react-intersection-observer": "~9.5.3", "reflect-metadata": "^0.1.13", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.26.0", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@rsbuild/core": "1.1.13", "@types/lodash-es": "^4.17.10", "@types/node": "18.18.9", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@types/react-helmet": "^6.1.11", "axios": "^1.7.1", "react": "~18.2.0", "react-dom": "~18.2.0", "react-helmet": "^6.1.0", "react-is": ">= 16.8.0", "react-router-dom": "^6.22.0", "styled-components": ">= 2", "typescript": "~5.8.2", "webpack": "~5.91.0"}}