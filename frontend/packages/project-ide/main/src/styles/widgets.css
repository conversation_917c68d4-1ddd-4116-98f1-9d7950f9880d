/* stylelint-disable declaration-no-important */
/* stylelint-disable no-descending-specificity */
/* stylelint-disable selector-class-pattern */
/* stylelint-disable selector-pseudo-element-colon-notation */

.lm-Widget {
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.lm-Widget.lm-mod-hidden {
  display: none !important;
}

.lm-DockPanel {
  z-index: 0;
}

.lm-DockPanel-widget {
  z-index: 0;
}

.lm-DockPanel-tabBar {
  z-index: 1;
}

.lm-DockPanel-handle {
  z-index: 2;
}

.lm-DockPanel-handle.lm-mod-hidden {
  display: none !important;
}

.lm-DockPanel-handle:after {
  content: '';

  position: absolute;
  top: 0;
  left: 0;

  width: 100%;
  height: 100%;
}

.lm-DockPanel-handle[data-orientation='horizontal'] {
  cursor: ew-resize;
}

.lm-DockPanel-handle[data-orientation='vertical'] {
  cursor: ns-resize;
}

.lm-DockPanel-handle[data-orientation='horizontal']:after {
  left: 50%;
  transform: translateX(-50%);
  min-width: 8px;
}

.lm-DockPanel-handle[data-orientation='vertical']:after {
  top: 50%;
  transform: translateY(-50%);
  min-height: 8px;
}

.lm-DockPanel-overlay {
  pointer-events: none;
  z-index: 3;
  box-sizing: border-box;
}

.lm-DockPanel-overlay.lm-mod-hidden {
  display: none !important;
}

.lm-SplitPanel-child {
  z-index: 0;
}

.lm-SplitPanel-handle {
  z-index: 1;
}

.lm-SplitPanel-handle.lm-mod-hidden {
  display: none !important;
}

.lm-SplitPanel-handle:after {
  content: '';

  position: absolute;
  top: 0;
  left: 0;

  width: 100%;
  height: 100%;
}

.lm-SplitPanel[data-orientation='horizontal'] > .lm-SplitPanel-handle {
  cursor: ew-resize;
}

.lm-SplitPanel[data-orientation='vertical'] > .lm-SplitPanel-handle {
  cursor: ns-resize;
}

.lm-SplitPanel[data-orientation='horizontal'] > .lm-SplitPanel-handle:after {
  left: 50%;
  transform: translateX(-50%);
  min-width: 8px;
}

.lm-SplitPanel[data-orientation='vertical'] > .lm-SplitPanel-handle:after {
  top: 50%;
  transform: translateY(-50%);
  min-height: 8px;
}


.lm-TabBar {
  user-select: none;
  display: flex;
}

.lm-TabBar[data-orientation='horizontal'] {
  flex-direction: row;
  align-items: flex-end;
}

.lm-TabBar[data-orientation='vertical'] {
  flex-direction: column;
  align-items: flex-end;
}

.lm-TabBar-content {
  display: flex;
  flex: 1 1 auto;

  margin: 0;
  padding: 0;

  list-style-type: none;
}

.lm-TabBar[data-orientation='horizontal'] > .lm-TabBar-content {
  flex-direction: row;
}

.lm-TabBar[data-orientation='vertical'] > .lm-TabBar-content {
  flex-direction: column;
}

.lm-TabBar-tab {
  touch-action: none; /* Disable native Drag/Drop */

  overflow: hidden;
  display: flex;
  flex-direction: row;

  box-sizing: border-box;
}

.lm-TabBar-tabIcon,
.lm-TabBar-tabCloseIcon {
  flex: 0 0 auto;
}

.lm-TabBar-tabLabel {
  overflow: hidden;
  flex: 1 1 auto;
  white-space: nowrap;
}

.lm-TabBar-tabInput {
  user-select: all;
  box-sizing: border-box;
  width: 100%;
}

.lm-TabBar-tab.lm-mod-hidden {
  display: none !important;
}

.lm-TabBar-addButton.lm-mod-hidden {
  display: none !important;
}

.lm-TabBar.lm-mod-dragging .lm-TabBar-tab {
  position: relative;
}

.lm-TabBar.lm-mod-dragging[data-orientation='horizontal'] .lm-TabBar-tab {
  left: 0;
  transition: left 150ms ease;
}

.lm-TabBar.lm-mod-dragging[data-orientation='vertical'] .lm-TabBar-tab {
  top: 0;
  transition: top 150ms ease;
}

.lm-TabBar.lm-mod-dragging .lm-TabBar-tab.lm-mod-dragging {
  transition: none;
}

.lm-TabBar-tabLabel .lm-TabBar-tabInput {
  user-select: all;
  box-sizing: border-box;
  width: 100%;
  background: inherit;
}


.lm-TabPanel-tabBar {
  z-index: 1;
}

.lm-TabPanel-stackedPanel {
  z-index: 0;
}

