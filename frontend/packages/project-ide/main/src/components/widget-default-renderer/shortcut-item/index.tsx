/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import styles from './styles.module.less';

export const ShortcutItem = ({
  item,
}: {
  item: {
    key: string;
    label: string;
    keybinding: string[][];
  };
}) => {
  const { key, label, keybinding } = item;

  return (
    <div className={styles['shortcut-item']} key={key}>
      <div className={styles.label}>{label}</div>
      <div className={styles.keybinding}>
        {keybinding.map(bindings =>
          bindings.map(binding => (
            <div key={binding} className={styles['keybinding-block']}>
              {binding}
            </div>
          )),
        )}
      </div>
    </div>
  );
};
