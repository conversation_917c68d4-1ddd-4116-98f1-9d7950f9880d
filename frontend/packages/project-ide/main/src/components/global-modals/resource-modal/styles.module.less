.content {
  margin-top: 4px;
  font-size: 14px;
  line-height: 20px;
  color: var(--coz-fg-secondary);

  .error-container {
    display: flex;
    flex: 1 0 0;
    flex-direction: column;
    gap: 22px;
    align-items: center;
    justify-content: center;

    margin: 24px 0 22px;

    white-space: pre-wrap;
  }

  .description-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    margin: 28px 0 12px;

    text-align: center;

    .spin {
      margin-bottom: 12px;
    }
  }
}
