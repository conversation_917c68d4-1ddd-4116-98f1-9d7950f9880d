.title-container {
  margin: 0 8px;
}

.widget-title {
  display: flex;
  align-items: center;
  justify-content: space-between;

  :global {
    .semi-spin.coz-loading-wrapper {
      line-height: 0;
    }
  }
}

.title-label {
  display: flex;
  flex-grow: 1;
  flex-shrink: 1;
  column-gap: 8px;
  align-items: center;

  width: 0;
}

.label-icon {
  display: flex;
  align-items: center;
}

.label-text {
  flex-grow: 1;
  flex-shrink: 1;
  width: 0;
  line-height: 20px;
}

.close-icon {
  cursor: pointer;
  display: flex;
  align-items: center;
}
