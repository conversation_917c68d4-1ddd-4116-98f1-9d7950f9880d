.primary-sidebar {
  overflow: hidden;

  width: 100%;
  height: 100%;

  background: var(--coz-bg-max);
  border: 1px solid var(--coz-stroke-primary);
  border-bottom: none;
  border-radius: 8px 8px 0 0;
}

.primary-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
  padding: 0 14px;

  font-size: 14px;
  line-height: 20px;

  .title {
    font-weight: 500;
    color: var(--coz-fg-plus);
  }
}

.resource-list-wrapper {
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0;
    height: 10px;
    background: transparent;
  }

  &::-webkit-scrollbar:hover {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}
