/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import { Spin } from '@coze-arch/coze-design';
import { useCurrentWidget } from '@coze-project-ide/client';

import { type ProjectIDEWidget } from '@/widgets/project-ide-widget';
import { type RegistryHandler } from '@/types';

import { useMount } from './use-mount';
import { useLifeCycle } from './use-lifecycle';

export const withRegistryContent = (registry: RegistryHandler<any>) => {
  const WidgetComp = () => {
    const widget: ProjectIDEWidget = useCurrentWidget();

    const { context } = widget;

    useLifeCycle(registry, context, widget);

    const { loaded, mounted, content } = useMount(registry, widget);

    return loaded && mounted ? content : <Spin />;
  };
  return WidgetComp;
};
