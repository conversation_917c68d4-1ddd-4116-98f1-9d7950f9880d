/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import {
  useIDEService,
  ShortcutsService,
  CommandRegistry,
} from '@coze-project-ide/client';

export const useShortcuts = (commandId: string) => {
  const commandRegistry = useIDEService<CommandRegistry>(CommandRegistry);
  const shortcutsService = useIDEService<ShortcutsService>(ShortcutsService);

  const shortcut = shortcutsService.getShortcutByCommandId(commandId);
  const keybinding = shortcut.map(item => item.join(' ')).join('/');
  const label = commandRegistry.getCommand(commandId)?.label;

  return {
    keybinding,
    label,
  };
};
