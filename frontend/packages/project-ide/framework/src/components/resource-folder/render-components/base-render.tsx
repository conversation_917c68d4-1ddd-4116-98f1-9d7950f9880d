/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import { type CommonComponentProps, ResourceTypeEnum } from '../type';
import { FolderRender } from './folder-render';
import { FileRender } from './file-render';

const BaseRender: React.FC<CommonComponentProps> = ({ ...props }) => {
  const { resource, path } = props;

  const Component =
    resource.type === ResourceTypeEnum.Folder ? FolderRender : FileRender;
  if (!Component) {
    return <></>;
  }

  return (
    <Component
      key={`base-render-${resource.id}`}
      {...props}
      path={[...path, resource.id]}
    />
  );
};

export { BaseRender };
